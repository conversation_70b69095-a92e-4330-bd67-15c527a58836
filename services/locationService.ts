import * as Location from 'expo-location';
import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';
import { Task, UserLocation, LocationAlert } from '@/types';

class LocationService {
  private watchId: Location.LocationSubscription | null = null;
  private webIntervalId: NodeJS.Timeout | null = null;
  private currentLocation: UserLocation | null = null;
  private isTracking = false;
  private notifiedTasks = new Set<string>();

  async requestPermissions(): Promise<boolean> {
    try {
      // Request location permissions
      const { status: foregroundStatus } = await Location.requestForegroundPermissionsAsync();
      
      if (foregroundStatus !== 'granted') {
        return false;
      }

      // Request background location permissions for native platforms
      if (Platform.OS !== 'web') {
        const { status: backgroundStatus } = await Location.requestBackgroundPermissionsAsync();
      }

      // Request notification permissions
      const { status: notificationStatus } = await Notifications.requestPermissionsAsync();
      
      return foregroundStatus === 'granted' && notificationStatus === 'granted';
    } catch (error) {
      console.error('Error requesting permissions:', error);
      return false;
    }
  }

  async getCurrentLocation(): Promise<UserLocation | null> {
    try {
      const { status } = await Location.getForegroundPermissionsAsync();
      
      if (status !== 'granted') {
        return null;
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
      });

      const userLocation: UserLocation = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        accuracy: location.coords.accuracy ?? undefined,
      };

      this.currentLocation = userLocation;
      return userLocation;
    } catch (error) {
      console.error('Error getting current location:', error);
      return null;
    }
  }

  async startLocationTracking(onLocationUpdate: (location: UserLocation) => void): Promise<boolean> {
    // Prevent multiple tracking instances by stopping any existing tracking first
    if (this.isTracking) {
      await this.stopLocationTracking();
    }

    if (Platform.OS === 'web') {
      // Limited tracking on web - just get current location periodically
      const location = await this.getCurrentLocation();
      if (location) {
        onLocationUpdate(location);
        // Set up periodic updates for web with proper cleanup
        this.webIntervalId = setInterval(async () => {
          const newLocation = await this.getCurrentLocation();
          if (newLocation) {
            onLocationUpdate(newLocation);
          }
        }, 30000); // Every 30 seconds
        this.isTracking = true;
      }
      return !!location;
    }

    try {
      const { status } = await Location.getForegroundPermissionsAsync();
      
      if (status !== 'granted') {
        return false;
      }

      this.watchId = await Location.watchPositionAsync(
        {
          accuracy: Location.Accuracy.Balanced,
          timeInterval: 15000, // 15 seconds
          distanceInterval: 20, // 20 meters
        },
        (location) => {
          const userLocation: UserLocation = {
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
            accuracy: location.coords.accuracy ?? undefined,
          };
          
          this.currentLocation = userLocation;
          onLocationUpdate(userLocation);
        }
      );

      this.isTracking = true;
      return true;
    } catch (error) {
      console.error('Error starting location tracking:', error);
      return false;
    }
  }

  async stopLocationTracking(): Promise<void> {
    // Clean up native location watching
    if (this.watchId) {
      this.watchId.remove();
      this.watchId = null;
    }
    
    // Clean up web interval
    if (this.webIntervalId) {
      clearInterval(this.webIntervalId);
      this.webIntervalId = null;
    }
    
    this.isTracking = false;
  }

  calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = lat1 * Math.PI/180;
    const φ2 = lat2 * Math.PI/180;
    const Δφ = (lat2-lat1) * Math.PI/180;
    const Δλ = (lon2-lon1) * Math.PI/180;

    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    return R * c; // Distance in meters
  }

  async checkLocationAlerts(tasks: Task[], currentLocation: UserLocation): Promise<LocationAlert[]> {
    const alerts: LocationAlert[] = [];

    for (const task of tasks) {
      if (!task.location || task.completed) continue;

      const distance = this.calculateDistance(
        currentLocation.latitude,
        currentLocation.longitude,
        task.location.latitude,
        task.location.longitude
      );

      if (distance <= task.notificationRadius && !this.notifiedTasks.has(task.id)) {
        alerts.push({
          taskId: task.id,
          type: 'entering',
          distance: Math.round(distance),
        });
        this.notifiedTasks.add(task.id);
      } else if (distance > task.notificationRadius * 1.5) {
        // Reset notification when user moves away
        this.notifiedTasks.delete(task.id);
      }
    }

    return alerts;
  }

  async sendLocationNotification(task: Task, alert: LocationAlert): Promise<void> {
    if (Platform.OS === 'web') {
      // Web notifications are limited
      if ('Notification' in window && Notification.permission === 'granted') {
        new Notification(`📍 Location Reminder`, {
          body: `You're near ${task.location?.name || task.location?.address}. Don't forget: ${task.title}`,
          icon: '/favicon.png',
        });
      }
      return;
    }

    try {
      await Notifications.scheduleNotificationAsync({
        content: {
          title: `📍 Location Reminder`,
          body: `You're near ${task.location?.name || task.location?.address}. Don't forget: ${task.title}`,
          data: { taskId: task.id, type: alert.type },
        },
        trigger: null, // Send immediately
      });
    } catch (error) {
      console.error('Error sending notification:', error);
    }
  }

  async reverseGeocode(latitude: number, longitude: number): Promise<string> {
    try {
      const result = await Location.reverseGeocodeAsync({
        latitude,
        longitude,
      });

      if (result.length > 0) {
        const address = result[0];
        const addressParts = [
          address.name,
          address.street,
          address.city,
          address.region,
        ].filter(Boolean);
        
        return addressParts.join(', ') || 'Unknown location';
      }
    } catch (error) {
      console.error('Error reverse geocoding:', error);
    }
    
    return `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;
  }

  getCurrentLocationSync(): UserLocation | null {
    return this.currentLocation;
  }

  isCurrentlyTracking(): boolean {
    return this.isTracking;
  }
}

export const locationService = new LocationService();