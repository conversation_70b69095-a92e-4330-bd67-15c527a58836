import AsyncStorage from '@react-native-async-storage/async-storage';
import { Task, TaskCategory, DEFAULT_CATEGORIES } from '@/types';

const TASKS_KEY = '@locationTasks:tasks';
const CATEGORIES_KEY = '@locationTasks:categories';
const SETTINGS_KEY = '@locationTasks:settings';

export interface UserSettings {
  notificationsEnabled: boolean;
  locationPermissionGranted: boolean;
  defaultNotificationRadius: number;
  userName?: string;
}

class StorageService {
  async getTasks(): Promise<Task[]> {
    try {
      const tasksJson = await AsyncStorage.getItem(TASKS_KEY);
      if (!tasksJson) return [];
      
      const tasks = JSON.parse(tasksJson);
      return tasks.map((task: any) => ({
        ...task,
        createdAt: new Date(task.createdAt),
        completedAt: task.completedAt ? new Date(task.completedAt) : undefined,
        dueDate: task.dueDate ? new Date(task.dueDate) : undefined,
      }));
    } catch (error) {
      console.error('Error loading tasks:', error);
      return [];
    }
  }

  async saveTasks(tasks: Task[]): Promise<void> {
    try {
      const tasksJson = JSON.stringify(tasks);
      await AsyncStorage.setItem(TASKS_KEY, tasksJson);
    } catch (error) {
      console.error('Error saving tasks:', error);
      throw error;
    }
  }

  async getCategories(): Promise<TaskCategory[]> {
    try {
      const categoriesJson = await AsyncStorage.getItem(CATEGORIES_KEY);
      if (!categoriesJson) {
        await this.saveCategories(DEFAULT_CATEGORIES);
        return DEFAULT_CATEGORIES;
      }
      
      return JSON.parse(categoriesJson);
    } catch (error) {
      console.error('Error loading categories:', error);
      return DEFAULT_CATEGORIES;
    }
  }

  async saveCategories(categories: TaskCategory[]): Promise<void> {
    try {
      const categoriesJson = JSON.stringify(categories);
      await AsyncStorage.setItem(CATEGORIES_KEY, categoriesJson);
    } catch (error) {
      console.error('Error saving categories:', error);
      throw error;
    }
  }

  async getUserSettings(): Promise<UserSettings> {
    try {
      const settingsJson = await AsyncStorage.getItem(SETTINGS_KEY);
      if (!settingsJson) {
        const defaultSettings: UserSettings = {
          notificationsEnabled: true,
          locationPermissionGranted: false,
          defaultNotificationRadius: 100,
        };
        await this.saveUserSettings(defaultSettings);
        return defaultSettings;
      }
      
      return JSON.parse(settingsJson);
    } catch (error) {
      console.error('Error loading user settings:', error);
      return {
        notificationsEnabled: true,
        locationPermissionGranted: false,
        defaultNotificationRadius: 100,
      };
    }
  }

  async saveUserSettings(settings: UserSettings): Promise<void> {
    try {
      const settingsJson = JSON.stringify(settings);
      await AsyncStorage.setItem(SETTINGS_KEY, settingsJson);
    } catch (error) {
      console.error('Error saving user settings:', error);
      throw error;
    }
  }

  async clearAllData(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([TASKS_KEY, CATEGORIES_KEY, SETTINGS_KEY]);
    } catch (error) {
      console.error('Error clearing data:', error);
      throw error;
    }
  }
}

export const storageService = new StorageService();