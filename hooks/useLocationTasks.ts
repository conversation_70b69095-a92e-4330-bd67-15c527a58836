import { useState, useEffect, useCallback, useRef } from 'react';
import { Task, TaskCategory, UserLocation } from '@/types';
import { storageService } from '@/services/storageService';
import { locationService } from '@/services/locationService';

export interface LocationTasksState {
  tasks: Task[];
  categories: TaskCategory[];
  userLocation: UserLocation | null;
  isLoading: boolean;
  error: string | null;
  locationPermissionGranted: boolean;
}

export function useLocationTasks() {
  const [state, setState] = useState<LocationTasksState>({
    tasks: [],
    categories: [],
    userLocation: null,
    isLoading: true,
    error: null,
    locationPermissionGranted: false,
  });

  // Use refs to track the latest values without causing re-renders
  const tasksRef = useRef<Task[]>([]);
  const notifiedTasksRef = useRef(new Set<string>());

  // Update refs when state changes
  useEffect(() => {
    tasksRef.current = state.tasks;
  }, [state.tasks]);

  const loadData = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));
      
      const [tasks, categories, userSettings] = await Promise.all([
        storageService.getTasks(),
        storageService.getCategories(),
        storageService.getUserSettings(),
      ]);

      let userLocation = null;
      if (userSettings.locationPermissionGranted) {
        userLocation = await locationService.getCurrentLocation();
      }

      setState(prev => ({
        ...prev,
        tasks,
        categories,
        userLocation,
        locationPermissionGranted: userSettings.locationPermissionGranted,
        isLoading: false,
      }));

      // Log for debugging
      console.log('Tasks loaded:', tasks.length);
      console.log('Categories loaded:', categories.length);
      
    } catch (error) {
      console.error('Error loading data:', error);
      setState(prev => ({
        ...prev,
        error: 'Failed to load data',
        isLoading: false,
      }));
    }
  }, []);

  const requestLocationPermission = useCallback(async () => {
    try {
      const granted = await locationService.requestPermissions();
      
      if (granted) {
        const userSettings = await storageService.getUserSettings();
        await storageService.saveUserSettings({
          ...userSettings,
          locationPermissionGranted: true,
        });

        const userLocation = await locationService.getCurrentLocation();
        
        setState(prev => ({
          ...prev,
          locationPermissionGranted: true,
          userLocation,
        }));

        // Start location tracking
        locationService.startLocationTracking((location) => {
          setState(prev => ({ ...prev, userLocation: location }));
          
          // Check location alerts with the latest tasks
          checkLocationAlertsWithCurrentData(location);
        });
      }
      
      return granted;
    } catch (error) {
      setState(prev => ({ ...prev, error: 'Failed to request location permission' }));
      return false;
    }
  }, []);

  // Stable function that doesn't depend on state
  const checkLocationAlertsWithCurrentData = useCallback(async (currentLocation: UserLocation) => {
    const currentTasks = tasksRef.current;
    if (!currentLocation || currentTasks.length === 0) return;

    try {
      const alerts = await locationService.checkLocationAlerts(currentTasks, currentLocation);
      
      for (const alert of alerts) {
        // Check if we've already notified for this task
        if (notifiedTasksRef.current.has(alert.taskId)) continue;
        
        const task = currentTasks.find(t => t.id === alert.taskId);
        if (task) {
          await locationService.sendLocationNotification(task, alert);
          notifiedTasksRef.current.add(alert.taskId);
        }
      }

      // Clean up notifications for tasks that are no longer nearby
      const nearbyTaskIds = new Set(alerts.map(a => a.taskId));
      for (const taskId of notifiedTasksRef.current) {
        if (!nearbyTaskIds.has(taskId)) {
          notifiedTasksRef.current.delete(taskId);
        }
      }
    } catch (error) {
      console.error('Error checking location alerts:', error);
    }
  }, []);

  const addTask = useCallback(async (task: Omit<Task, 'id' | 'createdAt'>) => {
    try {
      const newTask: Task = {
        ...task,
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        createdAt: new Date(),
        notificationRadius: task.notificationRadius || 100,
      };

      const updatedTasks = [...state.tasks, newTask];
      await storageService.saveTasks(updatedTasks);
      
      setState(prev => ({ ...prev, tasks: updatedTasks }));
      
      console.log('Task added:', newTask.title);
      return newTask;
    } catch (error) {
      console.error('Error adding task:', error);
      setState(prev => ({ ...prev, error: 'Failed to add task' }));
      throw error;
    }
  }, [state.tasks]);

  const updateTask = useCallback(async (taskId: string, updates: Partial<Task>) => {
    try {
      const updatedTasks = state.tasks.map(task => 
        task.id === taskId ? { ...task, ...updates } : task
      );
      
      await storageService.saveTasks(updatedTasks);
      setState(prev => ({ ...prev, tasks: updatedTasks }));
      
      console.log('Task updated:', taskId);
    } catch (error) {
      console.error('Error updating task:', error);
      setState(prev => ({ ...prev, error: 'Failed to update task' }));
      throw error;
    }
  }, [state.tasks]);

  const deleteTask = useCallback(async (taskId: string) => {
    try {
      const updatedTasks = state.tasks.filter(task => task.id !== taskId);
      await storageService.saveTasks(updatedTasks);
      setState(prev => ({ ...prev, tasks: updatedTasks }));
      
      // Clean up notification tracking for deleted task
      notifiedTasksRef.current.delete(taskId);
      
      console.log('Task deleted:', taskId);
    } catch (error) {
      console.error('Error deleting task:', error);
      setState(prev => ({ ...prev, error: 'Failed to delete task' }));
      throw error;
    }
  }, [state.tasks]);

  const toggleTaskCompletion = useCallback(async (taskId: string) => {
    try {
      const task = state.tasks.find(t => t.id === taskId);
      if (!task) return;

      const updates: Partial<Task> = {
        completed: !task.completed,
        completedAt: !task.completed ? new Date() : undefined,
      };

      await updateTask(taskId, updates);
      
      // Clean up notification tracking for completed tasks
      if (!task.completed) {
        notifiedTasksRef.current.delete(taskId);
      }
      
      console.log('Task completion toggled:', taskId, !task.completed);
    } catch (error) {
      console.error('Error toggling task completion:', error);
      setState(prev => ({ ...prev, error: 'Failed to toggle task completion' }));
    }
  }, [state.tasks, updateTask]);

  const getTasksNearLocation = useCallback((maxDistance: number = 1000) => {
    if (!state.userLocation) return [];

    return state.tasks.filter(task => {
      if (!task.location || task.completed) return false;
      
      const distance = locationService.calculateDistance(
        state.userLocation!.latitude,
        state.userLocation!.longitude,
        task.location.latitude,
        task.location.longitude
      );
      
      return distance <= maxDistance;
    });
  }, [state.tasks, state.userLocation]);

  // Initial data load
  useEffect(() => {
    loadData();
  }, [loadData]);

  // Location alerts check - only when location changes, not when tasks change
  useEffect(() => {
    if (state.locationPermissionGranted && state.userLocation) {
      checkLocationAlertsWithCurrentData(state.userLocation);
    }
  }, [state.userLocation, state.locationPermissionGranted, checkLocationAlertsWithCurrentData]);

  return {
    state,
    actions: {
      loadData,
      requestLocationPermission,
      addTask,
      updateTask,
      deleteTask,
      toggleTaskCompletion,
      getTasksNearLocation,
    },
  };
}