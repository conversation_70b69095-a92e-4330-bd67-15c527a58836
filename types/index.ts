export interface Task {
  id: string;
  title: string;
  description?: string;
  category: TaskCategory;
  location?: TaskLocation;
  completed: boolean;
  createdAt: Date;
  completedAt?: Date;
  priority: 'low' | 'medium' | 'high';
  dueDate?: Date;
  notificationRadius: number;
}

export interface TaskLocation {
  latitude: number;
  longitude: number;
  address: string;
  name?: string;
}

export interface TaskCategory {
  id: string;
  name: string;
  color: string;
  icon: string;
}

export interface UserLocation {
  latitude: number;
  longitude: number;
  accuracy?: number;
}

export interface LocationAlert {
  taskId: string;
  type: 'entering' | 'leaving';
  distance: number;
}

export const DEFAULT_CATEGORIES: TaskCategory[] = [
  { id: 'home', name: 'Home', color: '#6366F1', icon: 'home' },
  { id: 'work', name: 'Work', color: '#8B5CF6', icon: 'briefcase' },
  { id: 'shopping', name: 'Shopping', color: '#10B981', icon: 'shopping-bag' },
  { id: 'health', name: 'Health', color: '#F59E0B', icon: 'heart' },
  { id: 'family', name: 'Family', color: '#EF4444', icon: 'users' },
  { id: 'personal', name: 'Personal', color: '#06B6D4', icon: 'user' },
];