{"expo": {"name": "LocationTasks", "slug": "location-tasks", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "locationtasks", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "infoPlist": {"NSLocationWhenInUseUsageDescription": "This app needs location access to remind you of tasks when you're nearby.", "NSLocationAlwaysAndWhenInUseUsageDescription": "This app needs location access to remind you of tasks when you're nearby."}}, "android": {"permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "ACCESS_BACKGROUND_LOCATION"]}, "web": {"bundler": "metro", "output": "single", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "expo-font", "expo-location", "expo-notifications", "expo-task-manager"], "experiments": {"typedRoutes": true}}}