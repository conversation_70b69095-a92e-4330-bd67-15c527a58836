# LocationTasks

A location-based task management app that reminds you of tasks when you're near specific locations. Built with React Native and Expo.

## 🚀 Features

- **Location-Based Reminders**: Get notified about tasks when you enter or leave specific locations
- **Task Categories**: Organize tasks with predefined categories (Home, Work, Shopping, Health, Family, Personal)
- **Priority Management**: Set task priorities (low, medium, high) and due dates
- **Background Location Tracking**: Continuous location monitoring for timely notifications
- **Cross-Platform**: Works on iOS, Android, and Web
- **Offline Storage**: Tasks are stored locally using AsyncStorage

## 📱 Screenshots

*Add screenshots of your app here*

## 🛠️ Tech Stack

- **Framework**: React Native with Expo
- **Navigation**: Expo Router
- **Location Services**: Expo Location
- **Notifications**: Expo Notifications
- **Storage**: AsyncStorage
- **UI Components**: Custom components with Lucide React Native icons
- **TypeScript**: Full TypeScript support

## 📋 Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Expo CLI
- iOS Simulator (for iOS development)
- Android Studio/Emulator (for Android development)

## 🚀 Getting Started

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd hackathon1-main
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm run dev
   ```

4. **Run on your preferred platform**
   - Press `i` for iOS simulator
   - Press `a` for Android emulator
   - Press `w` for web browser
   - Scan QR code with Expo Go app for physical device

## 📁 Project Structure

```
├── app/                    # App screens and navigation
│   ├── (tabs)/            # Tab-based navigation screens
│   │   ├── index.tsx      # Main tasks screen
│   │   ├── add-task.tsx   # Add new task screen
│   │   ├── categories.tsx # Categories management
│   │   ├── locations.tsx  # Location-based tasks
│   │   └── profile.tsx    # User profile
│   ├── _layout.tsx        # Root layout
│   └── +not-found.tsx     # 404 screen
├── components/            # Reusable UI components
│   ├── TaskCard.tsx       # Individual task display
│   ├── CategoryFilter.tsx # Category filtering
│   └── LocationPicker.tsx # Location selection
├── hooks/                 # Custom React hooks
│   ├── useLocationTasks.ts # Task management logic
│   └── useFrameworkReady.ts # App initialization
├── services/              # Business logic services
│   ├── locationService.ts # Location tracking & notifications
│   └── storageService.ts  # Local data persistence
├── types/                 # TypeScript type definitions
│   └── index.ts          # App-wide interfaces
└── assets/               # Static assets (images, fonts)
```

## 🔧 Configuration

### Location Permissions

The app requires location permissions to function properly:

- **iOS**: Location access is requested for "when in use" and "always" modes
- **Android**: Fine location, coarse location, and background location permissions
- **Web**: Browser geolocation API

### Notification Setup

Push notifications are configured to alert users when they're near task locations:

- Customizable notification radius per task
- Background notification support
- Cross-platform notification handling

## 📝 Usage

1. **Add a Task**: Tap the "+" button to create a new task
2. **Set Location**: Choose a specific location for your task
3. **Configure Notifications**: Set the radius for location-based alerts
4. **Categorize**: Assign tasks to categories for better organization
5. **Set Priority**: Mark tasks as low, medium, or high priority
6. **Get Notified**: Receive alerts when you're near task locations

## 🎯 Task Categories

The app includes six predefined categories:

- 🏠 **Home** - Personal tasks at home
- 💼 **Work** - Professional tasks and reminders
- 🛍️ **Shopping** - Shopping lists and errands
- ❤️ **Health** - Medical appointments and health reminders
- 👥 **Family** - Family-related tasks and events
- 👤 **Personal** - Individual goals and activities

## 🔄 Scripts

- `npm run dev` - Start development server
- `npm run build:web` - Build for web deployment
- `npm run lint` - Run ESLint

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Built with [Expo](https://expo.dev/)
- Icons by [Lucide](https://lucide.dev/)
- Location services powered by Expo Location API

## 📞 Support

If you encounter any issues or have questions, please open an issue on GitHub.

---

*Built during Hackathon 1 - Location-Based Task Management*
