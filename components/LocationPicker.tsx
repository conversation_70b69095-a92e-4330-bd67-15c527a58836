import React, { useState } from 'react';
import { View, Text, StyleSheet, TextInput, TouchableOpacity, Alert } from 'react-native';
import { TaskLocation } from '@/types';
import { MapPin, Search, Target, Navigation } from 'lucide-react-native';
import { locationService } from '@/services/locationService';

interface LocationPickerProps {
  onLocationSelect: (location: TaskLocation) => void;
  initialLocation?: TaskLocation;
}

export function LocationPicker({ onLocationSelect, initialLocation }: LocationPickerProps) {
  const [address, setAddress] = useState(initialLocation?.address || '');
  const [locationName, setLocationName] = useState(initialLocation?.name || '');
  const [isLoading, setIsLoading] = useState(false);

  const handleUseCurrentLocation = async () => {
    setIsLoading(true);
    try {
      const location = await locationService.getCurrentLocation();
      
      if (!location) {
        Alert.alert(
          'Location Error',
          'Unable to get your current location. Please check your location permissions.'
        );
        return;
      }

      const addressText = await locationService.reverseGeocode(
        location.latitude,
        location.longitude
      );

      const taskLocation: TaskLocation = {
        latitude: location.latitude,
        longitude: location.longitude,
        address: addressText,
        name: locationName.trim() || undefined,
      };

      setAddress(addressText);
      onLocationSelect(taskLocation);
    } catch (error) {
      Alert.alert('Error', 'Failed to get current location');
    } finally {
      setIsLoading(false);
    }
  };

  const handleManualLocation = () => {
    if (!address.trim()) {
      Alert.alert('Error', 'Please enter an address');
      return;
    }

    // For demo purposes, we'll use a mock location
    // In a real app, you'd use a geocoding service
    const mockLocation: TaskLocation = {
      latitude: 37.7749 + (Math.random() - 0.5) * 0.01,
      longitude: -122.4194 + (Math.random() - 0.5) * 0.01,
      address: address.trim(),
      name: locationName.trim() || undefined,
    };

    onLocationSelect(mockLocation);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Set Task Location</Text>
      
      <View style={styles.section}>
        <Text style={styles.label}>Location Name (Optional)</Text>
        <View style={styles.inputContainer}>
          <MapPin size={20} color="#6366F1" strokeWidth={2} />
          <TextInput
            style={styles.input}
            value={locationName}
            onChangeText={setLocationName}
            placeholder="e.g., Home, Office, Grocery Store"
            placeholderTextColor="#9CA3AF"
          />
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.label}>Address</Text>
        <View style={styles.inputContainer}>
          <Search size={20} color="#6366F1" strokeWidth={2} />
          <TextInput
            style={styles.input}
            value={address}
            onChangeText={setAddress}
            placeholder="Enter address or location"
            placeholderTextColor="#9CA3AF"
            multiline
          />
        </View>
      </View>

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.button, styles.currentLocationButton]}
          onPress={handleUseCurrentLocation}
          disabled={isLoading}
        >
          <Navigation size={20} color="#FFFFFF" strokeWidth={2} />
          <Text style={styles.buttonText}>
            {isLoading ? 'Getting Location...' : 'Use Current Location'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.confirmButton]}
          onPress={handleManualLocation}
        >
          <Target size={20} color="#FFFFFF" strokeWidth={2} />
          <Text style={styles.buttonText}>Confirm Location</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 24,
  },
  title: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 24,
    textAlign: 'center',
  },
  section: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#374151',
    marginBottom: 12,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    borderWidth: 1.5,
    borderColor: '#E5E7EB',
    paddingHorizontal: 16,
    paddingVertical: 4,
  },
  input: {
    flex: 1,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#111827',
    paddingVertical: 12,
    paddingLeft: 12,
  },
  buttonContainer: {
    gap: 16,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    gap: 12,
  },
  currentLocationButton: {
    backgroundColor: '#6366F1',
  },
  confirmButton: {
    backgroundColor: '#10B981',
  },
  buttonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
});