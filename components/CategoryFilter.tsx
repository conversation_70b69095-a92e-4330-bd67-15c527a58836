import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { TaskCategory } from '@/types';

interface CategoryFilterProps {
  categories: TaskCategory[];
  selectedCategoryId: string | null;
  onCategorySelect: (categoryId: string | null) => void;
}

export function CategoryFilter({ categories, selectedCategoryId, onCategorySelect }: CategoryFilterProps) {
  return (
    <View style={styles.container}>
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        <TouchableOpacity
          style={[
            styles.categoryChip,
            selectedCategoryId === null && styles.selectedChip,
          ]}
          onPress={() => onCategorySelect(null)}
        >
          <Text style={[
            styles.categoryText,
            selectedCategoryId === null && styles.selectedText,
          ]}>
            All
          </Text>
        </TouchableOpacity>
        
        {categories.map((category) => (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.categoryChip,
              selectedCategoryId === category.id && styles.selectedChip,
              { borderColor: category.color }
            ]}
            onPress={() => onCategorySelect(category.id)}
          >
            <View style={[styles.colorDot, { backgroundColor: category.color }]} />
            <Text style={[
              styles.categoryText,
              selectedCategoryId === category.id && styles.selectedText,
            ]}>
              {category.name}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingVertical: 12,
    backgroundColor: '#FAFAFA',
  },
  scrollContent: {
    paddingHorizontal: 20,
    gap: 12,
  },
  categoryChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 24,
    borderWidth: 1.5,
    borderColor: '#E5E7EB',
    backgroundColor: '#FFFFFF',
    gap: 8,
  },
  selectedChip: {
    backgroundColor: '#6366F1',
    borderColor: '#6366F1',
  },
  colorDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  categoryText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  selectedText: {
    color: '#FFFFFF',
  },
});