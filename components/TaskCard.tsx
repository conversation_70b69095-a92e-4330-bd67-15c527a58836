import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Pressable } from 'react-native';
import { Task } from '@/types';
import { CircleCheck as CheckCircle, Circle, MapPin, Clock, TriangleAlert as AlertTriangle } from 'lucide-react-native';
import { locationService } from '@/services/locationService';

interface TaskCardProps {
  task: Task;
  userLocation?: { latitude: number; longitude: number } | null;
  onToggleComplete: (taskId: string) => void;
  onPress?: (task: Task) => void;
}

export function TaskCard({ task, userLocation, onToggleComplete, onPress }: TaskCardProps) {
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return '#EF4444';
      case 'medium': return '#F59E0B';
      case 'low': return '#10B981';
      default: return '#6B7280';
    }
  };

  const getDistanceText = () => {
    if (!task.location || !userLocation) return null;
    
    const distance = locationService.calculateDistance(
      userLocation.latitude,
      userLocation.longitude,
      task.location.latitude,
      task.location.longitude
    );

    if (distance < 1000) {
      return `${Math.round(distance)}m away`;
    } else {
      return `${(distance / 1000).toFixed(1)}km away`;
    }
  };

  const isNearby = () => {
    if (!task.location || !userLocation) return false;
    
    const distance = locationService.calculateDistance(
      userLocation.latitude,
      userLocation.longitude,
      task.location.latitude,
      task.location.longitude
    );
    
    return distance <= task.notificationRadius;
  };

  const distanceText = getDistanceText();
  const nearby = isNearby();

  return (
    <Pressable
      style={({ pressed }) => [
        styles.container,
        task.completed && styles.completedContainer,
        nearby && styles.nearbyContainer,
        pressed && styles.pressed,
      ]}
      onPress={() => onPress?.(task)}
    >
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.checkboxContainer}
          onPress={() => onToggleComplete(task.id)}
        >
          {task.completed ? (
            <CheckCircle size={24} color="#10B981" strokeWidth={2} />
          ) : (
            <Circle size={24} color="#9CA3AF" strokeWidth={2} />
          )}
        </TouchableOpacity>
        
        <View style={styles.content}>
          <Text style={[styles.title, task.completed && styles.completedTitle]}>
            {task.title}
          </Text>
          
          {task.description && (
            <Text style={[styles.description, task.completed && styles.completedText]}>
              {task.description}
            </Text>
          )}
          
          <View style={styles.metadata}>
            <View style={[styles.categoryBadge, { backgroundColor: task.category.color }]}>
              <Text style={styles.categoryText}>{task.category.name}</Text>
            </View>
            
            <View style={[styles.priorityBadge, { backgroundColor: getPriorityColor(task.priority) }]} />
          </View>
          
          {task.location && (
            <View style={styles.locationInfo}>
              <MapPin size={16} color="#6B7280" strokeWidth={2} />
              <Text style={styles.locationText} numberOfLines={1}>
                {task.location.name || task.location.address}
              </Text>
              {distanceText && (
                <Text style={[styles.distanceText, nearby && styles.nearbyText]}>
                  {distanceText}
                </Text>
              )}
            </View>
          )}
          
          {nearby && (
            <View style={styles.nearbyAlert}>
              <AlertTriangle size={16} color="#F59E0B" strokeWidth={2} />
              <Text style={styles.nearbyAlertText}>You're nearby!</Text>
            </View>
          )}
          
          {task.dueDate && (
            <View style={styles.dueDateInfo}>
              <Clock size={16} color="#6B7280" strokeWidth={2} />
              <Text style={styles.dueDateText}>
                Due {task.dueDate.toLocaleDateString()}
              </Text>
            </View>
          )}
        </View>
      </View>
    </Pressable>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginVertical: 8,
    marginHorizontal: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 4,
    borderWidth: 1,
    borderColor: '#F3F4F6',
  },
  completedContainer: {
    opacity: 0.7,
    backgroundColor: '#FAFAFA',
  },
  nearbyContainer: {
    borderColor: '#F59E0B',
    borderWidth: 2,
    backgroundColor: '#FFFBEB',
  },
  pressed: {
    transform: [{ scale: 0.98 }],
  },
  header: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  checkboxContainer: {
    marginRight: 16,
    marginTop: 2,
  },
  content: {
    flex: 1,
  },
  title: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 6,
    lineHeight: 24,
  },
  completedTitle: {
    textDecorationLine: 'line-through',
    color: '#6B7280',
  },
  description: {
    fontSize: 15,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginBottom: 12,
    lineHeight: 22,
  },
  completedText: {
    color: '#9CA3AF',
  },
  metadata: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  categoryBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 12,
  },
  categoryText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#FFFFFF',
  },
  priorityBadge: {
    width: 10,
    height: 10,
    borderRadius: 5,
  },
  locationInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  locationText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginLeft: 6,
    flex: 1,
  },
  distanceText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#9CA3AF',
    marginLeft: 8,
  },
  nearbyText: {
    color: '#F59E0B',
  },
  nearbyAlert: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FEF3C7',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 12,
    marginBottom: 6,
  },
  nearbyAlertText: {
    fontSize: 13,
    fontFamily: 'Inter-Medium',
    color: '#92400E',
    marginLeft: 6,
  },
  dueDateInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dueDateText: {
    fontSize: 13,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginLeft: 6,
  },
});