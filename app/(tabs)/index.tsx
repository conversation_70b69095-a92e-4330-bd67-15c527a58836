import React, { useState, useCallback } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, RefreshControl, Image } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useFocusEffect } from '@react-navigation/native';
import { TaskCard } from '@/components/TaskCard';
import { CategoryFilter } from '@/components/CategoryFilter';
import { useLocationTasks } from '@/hooks/useLocationTasks';
import { Task } from '@/types';
import { MapPin, Bell, BellOff, Plus, RefreshCw } from 'lucide-react-native';
import { router } from 'expo-router';

export default function TasksScreen() {
  const { state, actions } = useLocationTasks();
  const [selectedCategoryId, setSelectedCategoryId] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  // Force refresh data when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      actions.loadData();
    }, [actions])
  );

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await actions.loadData();
    } catch (error) {
      console.error('Error refreshing tasks:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleRequestLocationPermission = async () => {
    const granted = await actions.requestLocationPermission();
    if (!granted) {
      // Handle permission denied
    }
  };

  const filteredTasks = selectedCategoryId
    ? state.tasks.filter(task => task.category.id === selectedCategoryId)
    : state.tasks;

  const activeTasks = filteredTasks.filter(task => !task.completed);
  const completedTasks = filteredTasks.filter(task => task.completed);

  // Sort tasks by creation date (newest first) and then by priority
  const sortedActiveTasks = activeTasks.sort((a, b) => {
    const priorityOrder = { high: 3, medium: 2, low: 1 };
    if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    }
    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
  });

  const sortedCompletedTasks = completedTasks.sort((a, b) => {
    return new Date(b.completedAt || b.createdAt).getTime() - new Date(a.completedAt || a.createdAt).getTime();
  });

  const allSortedTasks = [...sortedActiveTasks, ...sortedCompletedTasks];

  const nearbyTasks = actions.getTasksNearLocation(1000);

  const renderTask = ({ item }: { item: Task }) => (
    <TaskCard
      task={item}
      userLocation={state.userLocation}
      onToggleComplete={actions.toggleTaskCompletion}
      onPress={(task) => {
        // Handle task details navigation
        console.log('Task pressed:', task.title);
      }}
    />
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Image
        source={{ uri: 'https://images.pexels.com/photos/3184291/pexels-photo-3184291.jpeg?auto=compress&cs=tinysrgb&w=400' }}
        style={styles.emptyImage}
      />
      <Text style={styles.emptyTitle}>No tasks yet</Text>
      <Text style={styles.emptyText}>
        Create your first location-based task to get started!
      </Text>
      <TouchableOpacity
        style={styles.addButton}
        onPress={() => router.push('/(tabs)/add')}
      >
        <Plus size={20} color="#FFFFFF" strokeWidth={2} />
        <Text style={styles.addButtonText}>Add Your First Task</Text>
      </TouchableOpacity>
    </View>
  );

  const renderTaskStats = () => (
    <View style={styles.statsContainer}>
      <View style={styles.statItem}>
        <Text style={styles.statNumber}>{activeTasks.length}</Text>
        <Text style={styles.statLabel}>Active</Text>
      </View>
      <View style={styles.statDivider} />
      <View style={styles.statItem}>
        <Text style={styles.statNumber}>{completedTasks.length}</Text>
        <Text style={styles.statLabel}>Completed</Text>
      </View>
      <View style={styles.statDivider} />
      <View style={styles.statItem}>
        <Text style={styles.statNumber}>
          {state.tasks.filter(t => t.location).length}
        </Text>
        <Text style={styles.statLabel}>With Location</Text>
      </View>
    </View>
  );

  if (state.isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <RefreshCw size={32} color="#6366F1" strokeWidth={2} />
          <Text style={styles.loadingText}>Loading your tasks...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <View>
          <Text style={styles.title}>My Tasks</Text>
          <Text style={styles.subtitle}>
            {state.tasks.length} total task{state.tasks.length !== 1 ? 's' : ''}
          </Text>
        </View>
        
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.refreshButton}
            onPress={onRefresh}
            disabled={refreshing}
          >
            <RefreshCw 
              size={20} 
              color="#6366F1" 
              strokeWidth={2}
              style={refreshing ? { transform: [{ rotate: '180deg' }] } : {}}
            />
          </TouchableOpacity>

          {!state.locationPermissionGranted && (
            <TouchableOpacity
              style={styles.locationPermissionButton}
              onPress={handleRequestLocationPermission}
            >
              <MapPin size={20} color="#6366F1" strokeWidth={2} />
            </TouchableOpacity>
          )}
          
          <TouchableOpacity style={styles.notificationButton}>
            {state.locationPermissionGranted ? (
              <Bell size={20} color="#10B981" strokeWidth={2} />
            ) : (
              <BellOff size={20} color="#9CA3AF" strokeWidth={2} />
            )}
          </TouchableOpacity>
        </View>
      </View>

      {state.userLocation && nearbyTasks.length > 0 && (
        <View style={styles.nearbySection}>
          <Text style={styles.nearbyTitle}>
            📍 {nearbyTasks.length} task{nearbyTasks.length !== 1 ? 's' : ''} nearby
          </Text>
          <Text style={styles.nearbySubtitle}>
            You'll be notified when you're in range
          </Text>
        </View>
      )}

      {state.tasks.length > 0 && renderTaskStats()}

      <CategoryFilter
        categories={state.categories}
        selectedCategoryId={selectedCategoryId}
        onCategorySelect={setSelectedCategoryId}
      />

      <FlatList
        data={allSortedTasks}
        renderItem={renderTask}
        keyExtractor={(item) => item.id}
        contentContainerStyle={[
          styles.listContainer,
          allSortedTasks.length === 0 && styles.emptyListContainer
        ]}
        refreshControl={
          <RefreshControl 
            refreshing={refreshing} 
            onRefresh={onRefresh}
            colors={['#6366F1']}
            tintColor="#6366F1"
          />
        }
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
        ItemSeparatorComponent={() => <View style={styles.taskSeparator} />}
      />

      {state.error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{state.error}</Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() => actions.loadData()}
          >
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFAFA',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  title: {
    fontSize: 28,
    fontFamily: 'Inter-Bold',
    color: '#111827',
  },
  subtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 2,
  },
  headerActions: {
    flexDirection: 'row',
    gap: 12,
  },
  refreshButton: {
    padding: 12,
    borderRadius: 12,
    backgroundColor: '#EEF2FF',
  },
  locationPermissionButton: {
    padding: 12,
    borderRadius: 12,
    backgroundColor: '#EEF2FF',
  },
  notificationButton: {
    padding: 12,
    borderRadius: 12,
    backgroundColor: '#F3F4F6',
  },
  nearbySection: {
    backgroundColor: '#FEF3C7',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  nearbyTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#92400E',
  },
  nearbySubtitle: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#A16207',
    marginTop: 2,
  },
  statsContainer: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    marginHorizontal: 20,
    marginTop: 16,
    borderRadius: 16,
    paddingVertical: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statDivider: {
    width: 1,
    backgroundColor: '#F3F4F6',
    marginVertical: 8,
  },
  statNumber: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  listContainer: {
    paddingBottom: 100,
  },
  emptyListContainer: {
    flex: 1,
  },
  taskSeparator: {
    height: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyImage: {
    width: 200,
    height: 150,
    borderRadius: 16,
    marginBottom: 24,
  },
  emptyTitle: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 12,
  },
  emptyText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#6366F1',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderRadius: 16,
    gap: 8,
  },
  addButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  errorContainer: {
    backgroundColor: '#FEE2E2',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: '#FECACA',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#DC2626',
    flex: 1,
  },
  retryButton: {
    backgroundColor: '#DC2626',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    marginLeft: 12,
  },
  retryButtonText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
});