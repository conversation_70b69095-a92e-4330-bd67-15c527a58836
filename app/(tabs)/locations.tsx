import React from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, Image } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocationTasks } from '@/hooks/useLocationTasks';
import { Task } from '@/types';
import { MapPin, Navigation, Clock } from 'lucide-react-native';
import { locationService } from '@/services/locationService';

export default function LocationsScreen() {
  const { state } = useLocationTasks();

  const tasksWithLocation = state.tasks.filter(task => task.location);
  
  const getDistanceText = (task: Task) => {
    if (!task.location || !state.userLocation) return null;
    
    const distance = locationService.calculateDistance(
      state.userLocation.latitude,
      state.userLocation.longitude,
      task.location.latitude,
      task.location.longitude
    );

    if (distance < 1000) {
      return `${Math.round(distance)}m away`;
    } else {
      return `${(distance / 1000).toFixed(1)}km away`;
    }
  };

  const renderLocationTask = ({ item }: { item: Task }) => {
    const distanceText = getDistanceText(item);
    
    return (
      <TouchableOpacity style={styles.locationCard}>
        <View style={styles.locationHeader}>
          <View style={styles.locationIcon}>
            <MapPin size={20} color="#6366F1" strokeWidth={2} />
          </View>
          <View style={styles.locationInfo}>
            <Text style={styles.locationName}>
              {item.location?.name || 'Unnamed Location'}
            </Text>
            <Text style={styles.locationAddress} numberOfLines={1}>
              {item.location?.address}
            </Text>
          </View>
          {distanceText && (
            <Text style={styles.distanceText}>{distanceText}</Text>
          )}
        </View>
        
        <View style={styles.taskInfo}>
          <Text style={styles.taskTitle}>{item.title}</Text>
          <View style={styles.taskMeta}>
            <View style={[styles.categoryBadge, { backgroundColor: item.category.color }]}>
              <Text style={styles.categoryText}>{item.category.name}</Text>
            </View>
            <View style={styles.radiusInfo}>
              <Navigation size={12} color="#6B7280" strokeWidth={2} />
              <Text style={styles.radiusText}>{item.notificationRadius}m radius</Text>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Image
        source={{ uri: 'https://images.pexels.com/photos/1252814/pexels-photo-1252814.jpeg?auto=compress&cs=tinysrgb&w=400' }}
        style={styles.emptyImage}
      />
      <Text style={styles.emptyTitle}>No location-based tasks</Text>
      <Text style={styles.emptyText}>
        Add locations to your tasks to get reminders when you're nearby
      </Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>My Locations</Text>
        <Text style={styles.subtitle}>
          {tasksWithLocation.length} location{tasksWithLocation.length !== 1 ? 's' : ''} tracked
        </Text>
      </View>

      {state.userLocation && (
        <View style={styles.currentLocationCard}>
          <View style={styles.currentLocationHeader}>
            <Navigation size={20} color="#10B981" strokeWidth={2} />
            <Text style={styles.currentLocationTitle}>Current Location</Text>
          </View>
          <Text style={styles.currentLocationText}>
            {state.userLocation.latitude.toFixed(6)}, {state.userLocation.longitude.toFixed(6)}
          </Text>
          {state.userLocation.accuracy && (
            <Text style={styles.accuracyText}>
              Accuracy: ±{Math.round(state.userLocation.accuracy)}m
            </Text>
          )}
        </View>
      )}

      <FlatList
        data={tasksWithLocation}
        renderItem={renderLocationTask}
        keyExtractor={(item) => item.id}
        contentContainerStyle={[
          styles.listContainer,
          tasksWithLocation.length === 0 && styles.emptyListContainer
        ]}
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFAFA',
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  title: {
    fontSize: 28,
    fontFamily: 'Inter-Bold',
    color: '#111827',
  },
  subtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 2,
  },
  currentLocationCard: {
    backgroundColor: '#F0FDF4',
    marginHorizontal: 20,
    marginTop: 16,
    padding: 16,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#BBF7D0',
  },
  currentLocationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  currentLocationTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#166534',
  },
  currentLocationText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#166534',
    fontVariant: ['tabular-nums'],
  },
  accuracyText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#16A34A',
    marginTop: 4,
  },
  listContainer: {
    padding: 20,
    paddingBottom: 100,
  },
  emptyListContainer: {
    flex: 1,
  },
  locationCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 4,
    borderWidth: 1,
    borderColor: '#F3F4F6',
  },
  locationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  locationIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#EEF2FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  locationInfo: {
    flex: 1,
  },
  locationName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 2,
  },
  locationAddress: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  distanceText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6366F1',
  },
  taskInfo: {
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
    paddingTop: 16,
  },
  taskTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#111827',
    marginBottom: 12,
  },
  taskMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  categoryBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  categoryText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#FFFFFF',
  },
  radiusInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  radiusText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyImage: {
    width: 200,
    height: 150,
    borderRadius: 16,
    marginBottom: 24,
  },
  emptyTitle: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#111827',
    marginBottom: 12,
  },
  emptyText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
  },
});