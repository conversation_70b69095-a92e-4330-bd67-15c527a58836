{"name": "location-tasks-app", "main": "expo-router/entry", "version": "1.0.0", "private": true, "scripts": {"dev": "EXPO_NO_TELEMETRY=1 expo start", "build:web": "expo export --platform web", "lint": "expo lint"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@expo-google-fonts/inter": "^0.2.3", "@react-native-async-storage/async-storage": "^2.1.0", "expo": "~52.0.30", "expo-blur": "~14.1.3", "expo-constants": "~17.1.3", "expo-font": "~13.2.2", "expo-haptics": "~14.1.3", "expo-linear-gradient": "~14.1.3", "expo-linking": "~7.1.3", "expo-location": "~18.1.4", "expo-notifications": "~0.30.4", "expo-router": "~4.0.17", "expo-splash-screen": "~0.30.6", "expo-status-bar": "~2.2.2", "expo-system-ui": "~5.0.5", "expo-task-manager": "~12.0.3", "expo-web-browser": "~14.1.5", "lucide-react-native": "^0.475.0", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "0.76.5", "react-native-gesture-handler": "~2.20.2", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.1.0", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~18.2.79", "typescript": "~5.8.3"}}